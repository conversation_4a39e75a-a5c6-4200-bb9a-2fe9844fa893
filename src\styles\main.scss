// ===== BASE STYLES =====
@import 'base/variables';
@import 'base/mixins';
@import 'base/reset';

// ===== THEMES =====
@import 'themes/light';
@import 'themes/dark';

// ===== COMPONENTS =====
@import 'components/navigation';
@import 'components/buttons';
@import 'components/cards';
@import 'components/forms';
@import 'components/animations';

// ===== PAGES =====
@import 'pages/home';
@import 'pages/project';
@import 'pages/about';

// ===== GLOBAL STYLES =====
.app {
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;
}

.main-content {
  position: relative;
  z-index: 1;
}

// ===== LOADING SCREEN =====
.loading-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-primary);
  @include flex-center;
  z-index: $z-index-modal;
  
  &__spinner {
    width: 60px;
    height: 60px;
    border: 3px solid var(--bg-tertiary);
    border-top: 3px solid var(--accent-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ===== SECTION WRAPPER =====
.section {
  @include section-padding;
  position: relative;
  
  &__container {
    @include container;
  }
  
  &__header {
    text-align: center;
    margin-bottom: $spacing-16;
    
    @include mobile-only {
      margin-bottom: $spacing-12;
    }
  }
  
  &__title {
    @include heading-2;
    margin-bottom: $spacing-4;
    
    &--gradient {
      @include text-gradient;
    }
  }
  
  &__subtitle {
    @include body-large;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
  }
}

// ===== BACKGROUND ELEMENTS =====
.bg-element {
  position: absolute;
  pointer-events: none;
  z-index: -1;
  
  &--blob {
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: var(--accent-primary);
    opacity: 0.1;
    filter: blur(40px);
    
    @include mobile-only {
      width: 200px;
      height: 200px;
    }
  }
  
  &--gradient {
    width: 500px;
    height: 500px;
    background: $gradient-primary;
    opacity: 0.05;
    filter: blur(60px);
    border-radius: 50%;
    
    @include mobile-only {
      width: 300px;
      height: 300px;
    }
  }
}

// ===== GRID LAYOUTS =====
.grid {
  display: grid;
  gap: $spacing-6;
  
  &--2 {
    grid-template-columns: repeat(2, 1fr);
    
    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }
  
  &--3 {
    grid-template-columns: repeat(3, 1fr);
    
    @include tablet-up {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }
  
  &--4 {
    grid-template-columns: repeat(4, 1fr);
    
    @include desktop-up {
      grid-template-columns: repeat(3, 1fr);
    }
    
    @include tablet-up {
      grid-template-columns: repeat(2, 1fr);
    }
    
    @include mobile-only {
      grid-template-columns: 1fr;
    }
  }
}

// ===== FLEX LAYOUTS =====
.flex {
  display: flex;
  
  &--center {
    @include flex-center;
  }
  
  &--between {
    @include flex-between;
  }
  
  &--column {
    @include flex-column;
  }
  
  &--wrap {
    flex-wrap: wrap;
  }
  
  &--gap-2 {
    gap: $spacing-2;
  }
  
  &--gap-4 {
    gap: $spacing-4;
  }
  
  &--gap-6 {
    gap: $spacing-6;
  }
  
  &--gap-8 {
    gap: $spacing-8;
  }
}

// ===== TEXT UTILITIES =====
.text {
  &--center {
    text-align: center;
  }
  
  &--left {
    text-align: left;
  }
  
  &--right {
    text-align: right;
  }
  
  &--gradient {
    @include text-gradient;
  }
  
  &--primary {
    color: var(--text-primary);
  }
  
  &--secondary {
    color: var(--text-secondary);
  }
  
  &--tertiary {
    color: var(--text-tertiary);
  }
}

// ===== SPACING UTILITIES =====
.mt {
  &-0 { margin-top: $spacing-0; }
  &-4 { margin-top: $spacing-4; }
  &-8 { margin-top: $spacing-8; }
  &-12 { margin-top: $spacing-12; }
  &-16 { margin-top: $spacing-16; }
  &-20 { margin-top: $spacing-20; }
}

.mb {
  &-0 { margin-bottom: $spacing-0; }
  &-4 { margin-bottom: $spacing-4; }
  &-8 { margin-bottom: $spacing-8; }
  &-12 { margin-bottom: $spacing-12; }
  &-16 { margin-bottom: $spacing-16; }
  &-20 { margin-bottom: $spacing-20; }
}

.pt {
  &-0 { padding-top: $spacing-0; }
  &-4 { padding-top: $spacing-4; }
  &-8 { padding-top: $spacing-8; }
  &-12 { padding-top: $spacing-12; }
  &-16 { padding-top: $spacing-16; }
  &-20 { padding-top: $spacing-20; }
}

.pb {
  &-0 { padding-bottom: $spacing-0; }
  &-4 { padding-bottom: $spacing-4; }
  &-8 { padding-bottom: $spacing-8; }
  &-12 { padding-bottom: $spacing-12; }
  &-16 { padding-bottom: $spacing-16; }
  &-20 { padding-bottom: $spacing-20; }
}
