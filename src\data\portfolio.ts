// ===== PORTFOLIO DATA =====

export interface Skill {
  name: string;
  category: 'frontend' | 'backend' | 'tools' | 'design';
  level: number; // 1-5
  icon?: string;
}

export interface TimelineItem {
  id: string;
  title: string;
  company: string;
  period: string;
  description: string;
  technologies: string[];
  type: 'work' | 'education' | 'project';
}

export interface Project {
  id: string;
  slug: string;
  title: string;
  subtitle: string;
  description: string;
  longDescription: string;
  image: string;
  images: string[];
  demoUrl?: string;
  githubUrl?: string;
  technologies: string[];
  category: 'web' | 'mobile' | 'desktop' | 'ai' | 'other';
  featured: boolean;
  year: number;
  challenges: string[];
  learnings: string[];
  features: string[];
}

export interface PersonalInfo {
  name: string;
  title: string;
  subtitle: string;
  bio: string;
  location: string;
  email: string;
  phone?: string;
  social: {
    github: string;
    linkedin: string;
    twitter?: string;
    dribbble?: string;
    behance?: string;
  };
  resume: string;
}

// ===== PERSONAL INFORMATION =====
export const personalInfo: PersonalInfo = {
  name: "<PERSON><PERSON><PERSON> Kumar",
  title: "Full-Stack Developer",
  subtitle: "Crafting Digital Experiences with Code & Creativity",
  bio: "I'm a passionate full-stack developer with 5+ years of experience creating innovative web applications. I specialize in React, Node.js, and modern web technologies, with a keen eye for design and user experience.",
  location: "San Francisco, CA",
  email: "<EMAIL>",
  phone: "+****************",
  social: {
    github: "https://github.com/nishant",
    linkedin: "https://linkedin.com/in/nishant",
    twitter: "https://twitter.com/nishant",
    dribbble: "https://dribbble.com/nishant",
  },
  resume: "/resume.pdf"
};

// ===== SKILLS DATA =====
export const skills: Skill[] = [
  // Frontend
  { name: "React", category: "frontend", level: 5 },
  { name: "TypeScript", category: "frontend", level: 5 },
  { name: "Next.js", category: "frontend", level: 4 },
  { name: "Vue.js", category: "frontend", level: 4 },
  { name: "GSAP", category: "frontend", level: 5 },
  { name: "Three.js", category: "frontend", level: 3 },
  { name: "SCSS/CSS", category: "frontend", level: 5 },
  { name: "Tailwind CSS", category: "frontend", level: 4 },
  
  // Backend
  { name: "Node.js", category: "backend", level: 5 },
  { name: "Express.js", category: "backend", level: 5 },
  { name: "Python", category: "backend", level: 4 },
  { name: "PostgreSQL", category: "backend", level: 4 },
  { name: "MongoDB", category: "backend", level: 4 },
  { name: "Redis", category: "backend", level: 3 },
  { name: "GraphQL", category: "backend", level: 4 },
  { name: "REST APIs", category: "backend", level: 5 },
  
  // Tools
  { name: "Git", category: "tools", level: 5 },
  { name: "Docker", category: "tools", level: 4 },
  { name: "AWS", category: "tools", level: 4 },
  { name: "Vercel", category: "tools", level: 5 },
  { name: "Webpack", category: "tools", level: 4 },
  { name: "Vite", category: "tools", level: 5 },
  { name: "Jest", category: "tools", level: 4 },
  { name: "Cypress", category: "tools", level: 3 },
  
  // Design
  { name: "Figma", category: "design", level: 4 },
  { name: "Adobe XD", category: "design", level: 3 },
  { name: "Photoshop", category: "design", level: 3 },
  { name: "UI/UX Design", category: "design", level: 4 },
];

// ===== TIMELINE DATA =====
export const timeline: TimelineItem[] = [
  {
    id: "1",
    title: "Senior Full-Stack Developer",
    company: "TechCorp Inc.",
    period: "2022 - Present",
    description: "Leading development of scalable web applications using React, Node.js, and cloud technologies. Mentoring junior developers and architecting system solutions.",
    technologies: ["React", "Node.js", "TypeScript", "AWS", "PostgreSQL"],
    type: "work"
  },
  {
    id: "2",
    title: "Full-Stack Developer",
    company: "StartupXYZ",
    period: "2020 - 2022",
    description: "Built and maintained multiple client projects from concept to deployment. Specialized in modern web technologies and responsive design.",
    technologies: ["Vue.js", "Express.js", "MongoDB", "Docker"],
    type: "work"
  },
  {
    id: "3",
    title: "Frontend Developer",
    company: "DesignStudio",
    period: "2019 - 2020",
    description: "Focused on creating pixel-perfect, animated user interfaces. Collaborated closely with designers to bring creative visions to life.",
    technologies: ["React", "GSAP", "SCSS", "Figma"],
    type: "work"
  },
  {
    id: "4",
    title: "Computer Science Degree",
    company: "University of Technology",
    period: "2015 - 2019",
    description: "Bachelor's degree in Computer Science with focus on web technologies and software engineering principles.",
    technologies: ["Java", "Python", "Algorithms", "Data Structures"],
    type: "education"
  }
];

// ===== PROJECTS DATA =====
export const projects: Project[] = [
  {
    id: "1",
    slug: "ecommerce-platform",
    title: "E-Commerce Platform",
    subtitle: "Modern shopping experience",
    description: "A full-featured e-commerce platform with real-time inventory, payment processing, and admin dashboard.",
    longDescription: "Built a comprehensive e-commerce solution from the ground up, featuring a modern React frontend, robust Node.js backend, and seamless payment integration. The platform handles thousands of products and users with real-time inventory management.",
    image: "/projects/ecommerce-hero.jpg",
    images: ["/projects/ecommerce-1.jpg", "/projects/ecommerce-2.jpg", "/projects/ecommerce-3.jpg"],
    demoUrl: "https://ecommerce-demo.com",
    githubUrl: "https://github.com/nishant/ecommerce",
    technologies: ["React", "Node.js", "PostgreSQL", "Stripe", "Redis", "Docker"],
    category: "web",
    featured: true,
    year: 2023,
    challenges: [
      "Implementing real-time inventory management",
      "Optimizing database queries for large product catalogs",
      "Ensuring secure payment processing"
    ],
    learnings: [
      "Advanced React patterns and state management",
      "Database optimization techniques",
      "Payment gateway integration best practices"
    ],
    features: [
      "Real-time inventory tracking",
      "Advanced search and filtering",
      "Secure payment processing",
      "Admin dashboard with analytics",
      "Mobile-responsive design"
    ]
  },
  {
    id: "2",
    slug: "task-management-app",
    title: "Task Management App",
    subtitle: "Productivity redefined",
    description: "A collaborative task management application with real-time updates, team collaboration, and advanced analytics.",
    longDescription: "Developed a comprehensive task management solution that helps teams stay organized and productive. Features include real-time collaboration, advanced project analytics, and intuitive drag-and-drop interfaces.",
    image: "/projects/taskapp-hero.jpg",
    images: ["/projects/taskapp-1.jpg", "/projects/taskapp-2.jpg", "/projects/taskapp-3.jpg"],
    demoUrl: "https://taskapp-demo.com",
    githubUrl: "https://github.com/nishant/taskapp",
    technologies: ["Vue.js", "Express.js", "MongoDB", "Socket.io", "Chart.js"],
    category: "web",
    featured: true,
    year: 2023,
    challenges: [
      "Implementing real-time collaboration features",
      "Creating intuitive drag-and-drop interfaces",
      "Building comprehensive analytics dashboard"
    ],
    learnings: [
      "WebSocket implementation for real-time features",
      "Advanced Vue.js composition API",
      "Data visualization with Chart.js"
    ],
    features: [
      "Real-time team collaboration",
      "Drag-and-drop task management",
      "Advanced project analytics",
      "Time tracking and reporting",
      "Mobile app companion"
    ]
  },
  {
    id: "3",
    slug: "ai-content-generator",
    title: "AI Content Generator",
    subtitle: "Powered by machine learning",
    description: "An AI-powered content generation tool that helps creators produce high-quality content using advanced language models.",
    longDescription: "Created an innovative AI content generation platform that leverages cutting-edge language models to help content creators, marketers, and writers produce high-quality content efficiently.",
    image: "/projects/ai-content-hero.jpg",
    images: ["/projects/ai-content-1.jpg", "/projects/ai-content-2.jpg", "/projects/ai-content-3.jpg"],
    demoUrl: "https://ai-content-demo.com",
    githubUrl: "https://github.com/nishant/ai-content",
    technologies: ["Next.js", "Python", "OpenAI API", "Supabase", "Tailwind CSS"],
    category: "ai",
    featured: true,
    year: 2023,
    challenges: [
      "Integrating multiple AI models effectively",
      "Optimizing API costs and response times",
      "Creating intuitive content editing interfaces"
    ],
    learnings: [
      "AI/ML integration in web applications",
      "Advanced Next.js features and optimization",
      "Cost-effective API usage strategies"
    ],
    features: [
      "Multiple AI model integration",
      "Real-time content generation",
      "Advanced editing tools",
      "Content optimization suggestions",
      "Team collaboration features"
    ]
  },
  {
    id: "4",
    slug: "portfolio-website",
    title: "Portfolio Website",
    subtitle: "This very website",
    description: "A cutting-edge portfolio website built with React, GSAP, and modern web technologies.",
    longDescription: "Designed and developed this award-worthy portfolio website using the latest web technologies and animation techniques. Features stunning GSAP animations, perfect Lighthouse scores, and responsive design.",
    image: "/projects/portfolio-hero.jpg",
    images: ["/projects/portfolio-1.jpg", "/projects/portfolio-2.jpg", "/projects/portfolio-3.jpg"],
    demoUrl: "https://nishant.dev",
    githubUrl: "https://github.com/nishant/portfolio",
    technologies: ["React", "TypeScript", "GSAP", "SCSS", "Vite"],
    category: "web",
    featured: true,
    year: 2024,
    challenges: [
      "Achieving 100% Lighthouse performance score",
      "Creating complex GSAP animations",
      "Implementing advanced scroll-triggered effects"
    ],
    learnings: [
      "Advanced GSAP animation techniques",
      "Performance optimization strategies",
      "Modern CSS and SCSS best practices"
    ],
    features: [
      "Award-worthy animations",
      "100% Lighthouse score",
      "Responsive design",
      "Dark/light theme toggle",
      "Advanced scroll effects"
    ]
  },
  {
    id: "5",
    slug: "weather-dashboard",
    title: "Weather Dashboard",
    subtitle: "Beautiful weather visualization",
    description: "A stunning weather dashboard with interactive maps, detailed forecasts, and beautiful data visualizations.",
    longDescription: "Built a comprehensive weather dashboard that provides detailed weather information with beautiful visualizations, interactive maps, and accurate forecasting using multiple weather APIs.",
    image: "/projects/weather-hero.jpg",
    images: ["/projects/weather-1.jpg", "/projects/weather-2.jpg", "/projects/weather-3.jpg"],
    demoUrl: "https://weather-dashboard-demo.com",
    githubUrl: "https://github.com/nishant/weather-dashboard",
    technologies: ["React", "D3.js", "Mapbox", "Weather APIs", "SCSS"],
    category: "web",
    featured: false,
    year: 2022,
    challenges: [
      "Integrating multiple weather data sources",
      "Creating smooth data visualizations",
      "Implementing interactive map features"
    ],
    learnings: [
      "Data visualization with D3.js",
      "Working with geolocation APIs",
      "Advanced map integration techniques"
    ],
    features: [
      "Interactive weather maps",
      "Detailed 7-day forecasts",
      "Beautiful data visualizations",
      "Location-based weather alerts",
      "Historical weather data"
    ]
  }
];
