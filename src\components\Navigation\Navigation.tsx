import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useTheme } from '@hooks/useTheme';

export const Navigation: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const { theme, toggleTheme } = useTheme();
  const location = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    setIsMenuOpen(false);
  }, [location]);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <nav className={`navigation ${isScrolled ? 'navigation--scrolled' : ''}`}>
      <div className="navigation__container">
        <Link to="/" className="navigation__logo">
          <span className="navigation__logo-text">NK</span>
        </Link>

        <div className={`navigation__menu ${isMenuOpen ? 'navigation__menu--open' : ''}`}>
          <Link to="/" className="navigation__link">
            Home
          </Link>
          <Link to="/about" className="navigation__link">
            About
          </Link>
          <a href="#projects" className="navigation__link">
            Projects
          </a>
          <a href="#contact" className="navigation__link">
            Contact
          </a>
        </div>

        <div className="navigation__actions">
          <button
            onClick={toggleTheme}
            className="navigation__theme-toggle"
            aria-label={`Switch to ${theme === 'light' ? 'dark' : 'light'} theme`}
          >
            {theme === 'light' ? '🌙' : '☀️'}
          </button>

          <button
            onClick={toggleMenu}
            className={`navigation__burger ${isMenuOpen ? 'navigation__burger--open' : ''}`}
            aria-label="Toggle menu"
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>
    </nav>
  );
};
