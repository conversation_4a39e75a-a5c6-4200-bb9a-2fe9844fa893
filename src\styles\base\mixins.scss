// ===== RESPONSIVE MIXINS =====
@mixin mobile-only {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet-up {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

@mixin desktop-up {
  @media (min-width: #{$breakpoint-lg}) {
    @content;
  }
}

@mixin large-desktop-up {
  @media (min-width: #{$breakpoint-xl}) {
    @content;
  }
}

@mixin xl-desktop-up {
  @media (min-width: #{$breakpoint-2xl}) {
    @content;
  }
}

@mixin between($min, $max) {
  @media (min-width: #{$min}) and (max-width: #{$max - 1px}) {
    @content;
  }
}

// ===== LAYOUT MIXINS =====
@mixin container {
  max-width: $container-max-width;
  margin: 0 auto;
  padding: 0 $container-padding;

  @include mobile-only {
    padding: 0 $spacing-4;
  }
}

@mixin section-padding {
  padding: $section-padding 0;

  @include mobile-only {
    padding: $section-padding-mobile 0;
  }
}

@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin flex-column {
  display: flex;
  flex-direction: column;
}

@mixin grid-center {
  display: grid;
  place-items: center;
}

@mixin absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@mixin absolute-fill {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

// ===== TYPOGRAPHY MIXINS =====
@mixin heading-1 {
  font-family: $font-secondary;
  font-size: $font-size-6xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;

  @include mobile-only {
    font-size: $font-size-4xl;
  }
}

@mixin heading-2 {
  font-family: $font-secondary;
  font-size: $font-size-5xl;
  font-weight: $font-weight-bold;
  line-height: $line-height-tight;

  @include mobile-only {
    font-size: $font-size-3xl;
  }
}

@mixin heading-3 {
  font-family: $font-secondary;
  font-size: $font-size-4xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-snug;

  @include mobile-only {
    font-size: $font-size-2xl;
  }
}

@mixin heading-4 {
  font-family: $font-secondary;
  font-size: $font-size-3xl;
  font-weight: $font-weight-semibold;
  line-height: $line-height-snug;

  @include mobile-only {
    font-size: $font-size-xl;
  }
}

@mixin body-large {
  font-family: $font-primary;
  font-size: $font-size-lg;
  font-weight: $font-weight-regular;
  line-height: $line-height-relaxed;
}

@mixin body-base {
  font-family: $font-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-regular;
  line-height: $line-height-normal;
}

@mixin body-small {
  font-family: $font-primary;
  font-size: $font-size-sm;
  font-weight: $font-weight-regular;
  line-height: $line-height-normal;
}

@mixin text-gradient($gradient: $gradient-primary) {
  background: $gradient;
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
}

// ===== ANIMATION MIXINS =====
@mixin smooth-transition($property: all, $duration: $transition-base, $easing: $ease-out-cubic) {
  transition: $property $duration $easing;
}

@mixin hover-lift {
  @include smooth-transition(transform);
  
  &:hover {
    transform: translateY(-4px);
  }
}

@mixin hover-scale {
  @include smooth-transition(transform);
  
  &:hover {
    transform: scale(1.05);
  }
}

@mixin fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: opacity 0.6s $ease-out-cubic, transform 0.6s $ease-out-cubic;

  &.animate {
    opacity: 1;
    transform: translateY(0);
  }
}

@mixin fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
  transition: opacity 0.6s $ease-out-cubic, transform 0.6s $ease-out-cubic;

  &.animate {
    opacity: 1;
    transform: translateX(0);
  }
}

@mixin fade-in-right {
  opacity: 0;
  transform: translateX(30px);
  transition: opacity 0.6s $ease-out-cubic, transform 0.6s $ease-out-cubic;

  &.animate {
    opacity: 1;
    transform: translateX(0);
  }
}

@mixin scale-in {
  opacity: 0;
  transform: scale(0.8);
  transition: opacity 0.6s $ease-out-cubic, transform 0.6s $ease-out-cubic;

  &.animate {
    opacity: 1;
    transform: scale(1);
  }
}

// ===== VISUAL EFFECTS MIXINS =====
@mixin glass-morphism {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: $border-radius-lg;
}

@mixin neumorphism-light {
  background: var(--bg-primary);
  box-shadow: 
    8px 8px 16px rgba(0, 0, 0, 0.1),
    -8px -8px 16px rgba(255, 255, 255, 0.8);
}

@mixin neumorphism-dark {
  background: var(--bg-secondary);
  box-shadow: 
    8px 8px 16px rgba(0, 0, 0, 0.3),
    -8px -8px 16px rgba(255, 255, 255, 0.05);
}

@mixin gradient-border($gradient: $gradient-primary, $width: 2px) {
  position: relative;
  background: var(--bg-primary);
  
  &::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: $width;
    background: $gradient;
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
  }
}

@mixin animated-gradient($gradient: $gradient-primary) {
  background: $gradient;
  background-size: 200% 200%;
  animation: gradient-shift 3s ease infinite;
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

// ===== UTILITY MIXINS =====
@mixin visually-hidden {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

@mixin truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin line-clamp($lines: 2) {
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

@mixin focus-ring {
  outline: 2px solid var(--accent-primary);
  outline-offset: 2px;
}

@mixin reset-button {
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  cursor: pointer;
  font: inherit;
  color: inherit;
}

@mixin reset-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

// ===== ACCESSIBILITY MIXINS =====
@mixin reduced-motion {
  @media (prefers-reduced-motion: reduce) {
    @content;
  }
}

@mixin high-contrast {
  @media (prefers-contrast: high) {
    @content;
  }
}
