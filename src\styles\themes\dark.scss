// ===== DARK THEME =====
[data-theme="dark"] {
  // Background Colors
  --bg-primary: #{$primary-100};
  --bg-secondary: #{$primary-200};
  --bg-tertiary: #{$primary-300};
  --bg-quaternary: #{$primary-400};
  
  // Text Colors
  --text-primary: #{$white};
  --text-secondary: #{$gray-300};
  --text-tertiary: #{$gray-400};
  --text-quaternary: #{$gray-500};
  
  // Border Colors
  --border-primary: #{$primary-400};
  --border-secondary: #{$primary-300};
  --border-tertiary: #{$primary-200};
  
  // Shadow Colors
  --shadow-primary: rgba(0, 0, 0, 0.3);
  --shadow-secondary: rgba(0, 0, 0, 0.2);
  --shadow-tertiary: rgba(0, 0, 0, 0.1);
  
  // Accent Colors (same as light theme)
  --accent-primary: #{$accent-100};
  --accent-secondary: #{$accent-200};
  --accent-tertiary: #{$accent-300};
  --accent-quaternary: #{$accent-400};
  --accent-quinary: #{$accent-500};
  
  // Interactive States
  --hover-bg: #{$primary-300};
  --active-bg: #{$primary-400};
  --focus-ring: #{$accent-100};
  
  // Gradients
  --gradient-primary: #{$gradient-primary};
  --gradient-secondary: #{$gradient-secondary};
  --gradient-accent: #{$gradient-accent};
  
  // Special Effects
  --glass-bg: rgba(26, 26, 46, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --backdrop-blur: blur(20px);
  
  // Navigation
  --nav-bg: rgba(15, 15, 35, 0.9);
  --nav-border: rgba(255, 255, 255, 0.1);
  
  // Cards
  --card-bg: #{$primary-200};
  --card-border: #{$primary-400};
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --card-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -2px rgba(0, 0, 0, 0.3);
  
  // Buttons
  --btn-primary-bg: #{$accent-100};
  --btn-primary-text: #{$white};
  --btn-primary-hover: #{lighten($accent-100, 10%)};
  --btn-secondary-bg: transparent;
  --btn-secondary-text: #{$accent-100};
  --btn-secondary-border: #{$accent-100};
  --btn-secondary-hover-bg: #{$accent-100};
  --btn-secondary-hover-text: #{$white};
  
  // Forms
  --input-bg: #{$primary-300};
  --input-border: #{$primary-400};
  --input-focus-border: #{$accent-100};
  --input-placeholder: #{$gray-500};
  
  // Code
  --code-bg: #{$primary-300};
  --code-text: #{$gray-200};
  --code-border: #{$primary-400};
  
  // ===== DARK THEME SPECIFIC STYLES =====
  .hero {
    &__background {
      background: linear-gradient(135deg, 
        rgba(255, 107, 107, 0.15) 0%, 
        rgba(78, 205, 196, 0.15) 50%, 
        rgba(69, 183, 209, 0.15) 100%);
    }
  }
  
  .skill-chip {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-secondary);
    
    &:hover {
      background: var(--accent-primary);
      color: var(--btn-primary-text);
      border-color: var(--accent-primary);
    }
  }
  
  .timeline {
    &__line {
      background: var(--border-primary);
    }
    
    &__dot {
      background: var(--accent-primary);
      border: 3px solid var(--bg-primary);
    }
  }
  
  .project-card {
    &::before {
      background: linear-gradient(135deg, 
        rgba(255, 107, 107, 0.1) 0%, 
        rgba(78, 205, 196, 0.1) 100%);
    }
  }
  
  .blob {
    &--primary {
      background: radial-gradient(circle, 
        rgba(255, 107, 107, 0.3) 0%, 
        rgba(255, 107, 107, 0.15) 50%, 
        transparent 100%);
    }
    
    &--secondary {
      background: radial-gradient(circle, 
        rgba(78, 205, 196, 0.3) 0%, 
        rgba(78, 205, 196, 0.15) 50%, 
        transparent 100%);
    }
    
    &--tertiary {
      background: radial-gradient(circle, 
        rgba(69, 183, 209, 0.3) 0%, 
        rgba(69, 183, 209, 0.15) 50%, 
        transparent 100%);
    }
  }
  
  // Enhanced glow effects for dark theme
  .glow {
    &--primary {
      box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
    }
    
    &--secondary {
      box-shadow: 0 0 20px rgba(78, 205, 196, 0.3);
    }
    
    &--tertiary {
      box-shadow: 0 0 20px rgba(69, 183, 209, 0.3);
    }
  }
  
  // Particle effects
  .particle {
    &--primary {
      background: var(--accent-primary);
      box-shadow: 0 0 6px var(--accent-primary);
    }
    
    &--secondary {
      background: var(--accent-secondary);
      box-shadow: 0 0 6px var(--accent-secondary);
    }
    
    &--tertiary {
      background: var(--accent-tertiary);
      box-shadow: 0 0 6px var(--accent-tertiary);
    }
  }
}
