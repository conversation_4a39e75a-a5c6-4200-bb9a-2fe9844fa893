// ===== LIGHT THEME =====
:root {
  // Background Colors
  --bg-primary: #{$white};
  --bg-secondary: #{$gray-100};
  --bg-tertiary: #{$gray-200};
  --bg-quaternary: #{$gray-50};
  
  // Text Colors
  --text-primary: #{$gray-900};
  --text-secondary: #{$gray-700};
  --text-tertiary: #{$gray-600};
  --text-quaternary: #{$gray-500};
  
  // Border Colors
  --border-primary: #{$gray-300};
  --border-secondary: #{$gray-200};
  --border-tertiary: #{$gray-100};
  
  // Shadow Colors
  --shadow-primary: rgba(0, 0, 0, 0.1);
  --shadow-secondary: rgba(0, 0, 0, 0.05);
  --shadow-tertiary: rgba(0, 0, 0, 0.03);
  
  // Accent Colors
  --accent-primary: #{$accent-100};
  --accent-secondary: #{$accent-200};
  --accent-tertiary: #{$accent-300};
  --accent-quaternary: #{$accent-400};
  --accent-quinary: #{$accent-500};
  
  // Interactive States
  --hover-bg: #{$gray-50};
  --active-bg: #{$gray-100};
  --focus-ring: #{$accent-100};
  
  // Gradients
  --gradient-primary: #{$gradient-primary};
  --gradient-secondary: #{$gradient-secondary};
  --gradient-accent: #{$gradient-accent};
  
  // Special Effects
  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.3);
  --backdrop-blur: blur(20px);
  
  // Navigation
  --nav-bg: rgba(255, 255, 255, 0.9);
  --nav-border: rgba(0, 0, 0, 0.1);
  
  // Cards
  --card-bg: #{$white};
  --card-border: #{$gray-200};
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --card-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  // Buttons
  --btn-primary-bg: #{$accent-100};
  --btn-primary-text: #{$white};
  --btn-primary-hover: #{darken($accent-100, 10%)};
  --btn-secondary-bg: transparent;
  --btn-secondary-text: #{$accent-100};
  --btn-secondary-border: #{$accent-100};
  --btn-secondary-hover-bg: #{$accent-100};
  --btn-secondary-hover-text: #{$white};
  
  // Forms
  --input-bg: #{$white};
  --input-border: #{$gray-300};
  --input-focus-border: #{$accent-100};
  --input-placeholder: #{$gray-500};
  
  // Code
  --code-bg: #{$gray-100};
  --code-text: #{$gray-800};
  --code-border: #{$gray-200};
}

// ===== LIGHT THEME SPECIFIC STYLES =====
[data-theme="light"] {
  .hero {
    &__background {
      background: linear-gradient(135deg, 
        rgba(255, 107, 107, 0.1) 0%, 
        rgba(78, 205, 196, 0.1) 50%, 
        rgba(69, 183, 209, 0.1) 100%);
    }
  }
  
  .skill-chip {
    background: var(--bg-secondary);
    border: 1px solid var(--border-secondary);
    
    &:hover {
      background: var(--accent-primary);
      color: var(--btn-primary-text);
      border-color: var(--accent-primary);
    }
  }
  
  .timeline {
    &__line {
      background: var(--border-primary);
    }
    
    &__dot {
      background: var(--accent-primary);
      border: 3px solid var(--bg-primary);
    }
  }
  
  .project-card {
    &::before {
      background: linear-gradient(135deg, 
        rgba(255, 107, 107, 0.05) 0%, 
        rgba(78, 205, 196, 0.05) 100%);
    }
  }
  
  .blob {
    &--primary {
      background: radial-gradient(circle, 
        rgba(255, 107, 107, 0.2) 0%, 
        rgba(255, 107, 107, 0.1) 50%, 
        transparent 100%);
    }
    
    &--secondary {
      background: radial-gradient(circle, 
        rgba(78, 205, 196, 0.2) 0%, 
        rgba(78, 205, 196, 0.1) 50%, 
        transparent 100%);
    }
    
    &--tertiary {
      background: radial-gradient(circle, 
        rgba(69, 183, 209, 0.2) 0%, 
        rgba(69, 183, 209, 0.1) 50%, 
        transparent 100%);
    }
  }
}
