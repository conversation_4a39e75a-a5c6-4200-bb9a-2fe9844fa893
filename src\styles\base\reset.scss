// ===== MODERN CSS RESET =====
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  
  @include reduced-motion {
    scroll-behavior: auto;
  }
}

body {
  font-family: $font-primary;
  font-size: $font-size-base;
  font-weight: $font-weight-regular;
  line-height: $line-height-normal;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  overflow-x: hidden;
  
  @include reduced-motion {
    * {
      animation-duration: 0.01ms !important;
      animation-iteration-count: 1 !important;
      transition-duration: 0.01ms !important;
    }
  }
}

// ===== TYPOGRAPHY RESET =====
h1, h2, h3, h4, h5, h6 {
  font-weight: inherit;
  line-height: inherit;
  margin: 0;
}

p {
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
  
  &:focus-visible {
    @include focus-ring;
  }
}

// ===== FORM RESET =====
button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  font-size: 100%;
  line-height: 1.15;
  margin: 0;
}

button,
[type="button"],
[type="reset"],
[type="submit"] {
  -webkit-appearance: button;
  cursor: pointer;
  
  &:focus-visible {
    @include focus-ring;
  }
}

button::-moz-focus-inner,
[type="button"]::-moz-focus-inner,
[type="reset"]::-moz-focus-inner,
[type="submit"]::-moz-focus-inner {
  border-style: none;
  padding: 0;
}

input,
textarea {
  &:focus-visible {
    @include focus-ring;
  }
}

// ===== LIST RESET =====
ul, ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

// ===== MEDIA RESET =====
img,
picture,
video,
canvas,
svg {
  display: block;
  max-width: 100%;
  height: auto;
}

img {
  border-style: none;
}

svg {
  fill: currentColor;
}

// ===== TABLE RESET =====
table {
  border-collapse: collapse;
  border-spacing: 0;
}

// ===== SELECTION =====
::selection {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
}

::-moz-selection {
  background-color: var(--accent-primary);
  color: var(--bg-primary);
}

// ===== SCROLLBAR STYLING =====
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: $border-radius-full;
  
  &:hover {
    background: var(--accent-secondary);
  }
}

// ===== FOCUS MANAGEMENT =====
[tabindex="-1"]:focus:not(:focus-visible) {
  outline: 0 !important;
}

// ===== UTILITY CLASSES =====
.sr-only {
  @include visually-hidden;
}

.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--bg-primary);
  color: var(--text-primary);
  padding: 8px;
  text-decoration: none;
  border-radius: $border-radius-base;
  z-index: $z-index-tooltip;
  
  &:focus {
    top: 6px;
  }
}

// ===== THEME TRANSITION =====
* {
  @include smooth-transition((background-color, color, border-color, box-shadow));
}
